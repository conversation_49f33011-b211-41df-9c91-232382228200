//
//  PromotionCenterMainViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView

class PromotionCenterMainViewController: BaseViewController {
    
    // MARK: - Properties
    
    // 页面标题数组
    private let titles = ["带货中心", "销售中心", "我的带货"]
    
    // 子页面控制器数组
    private var subViewControllers: [UIViewController] = []
    
    // JX分段控制器
    private var segmentedView: JXSegmentedView!
    private var segmentedDataSource: JXSegmentedTitleDataSource!
    private var listContainerView: JXSegmentedListContainerView!

    // 自定义底部TabBar
    private var customBottomTabBar: PromotionCenterTabBar!
    
    // 当前选中的索引
    private var currentIndex: Int = 0
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置标题
        navTitle = "带货中心"

        // 设置视图背景色
        view.backgroundColor = .white
        contentView.backgroundColor = .white

        // 初始化子控制器
        setupSubViewControllers()

        // 设置UI
        setupUI()

        // 设置底部TabBar
        setupBottomTabBar()

        // 设置初始导航栏样式
        updateNavigationBarStyle(for: 0)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 确保导航栏在最上层
        bringNavBarToFront()
    }
    
    // MARK: - Setup
    private func setupSubViewControllers() {
        // 创建三个子页面控制器
        let promotionCenterVC = PromotionCenterViewController()
        let salesCenterVC = SalesCenterViewController()
        let myPromotionVC = MyPromotionViewController()

        subViewControllers = [promotionCenterVC, salesCenterVC, myPromotionVC]
    }
    
    private func setupUI() {
        // 隐藏顶部的JX分段控制器，因为我们使用底部TabBar
        // setupSegmentedView()

        // 设置列表容器
        setupListContainer()
    }
    
    private func setupSegmentedView() {
        // 创建数据源
        segmentedDataSource = JXSegmentedTitleDataSource()
        segmentedDataSource.titles = titles
        segmentedDataSource.titleNormalColor = UIColor(hex: "#666666")
        segmentedDataSource.titleSelectedColor = UIColor(hex: "#FB6C04")
        segmentedDataSource.titleNormalFont = UIFont.systemFont(ofSize: 16)
        segmentedDataSource.titleSelectedFont = UIFont.boldSystemFont(ofSize: 16)
        segmentedDataSource.itemSpacing = 40
        segmentedDataSource.isItemSpacingAverageEnabled = false
        
        // 创建分段控制器
        segmentedView = JXSegmentedView()
        segmentedView.backgroundColor = .white
        segmentedView.delegate = self
        segmentedView.dataSource = segmentedDataSource
        
        // 创建指示器
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor(hex: "#FB6C04")
        indicator.indicatorWidth = 20
        indicator.indicatorHeight = 3
        indicator.indicatorCornerRadius = 1.5
        segmentedView.indicators = [indicator]
        
        // 添加到视图
        contentView.addSubview(segmentedView)
        segmentedView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
    }
    
    private func setupListContainer() {
        // 创建列表容器
        listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.backgroundColor = .white

        // 添加到视图
        contentView.addSubview(listContainerView)
        listContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-59) // 为底部TabBar留出空间
        }
    }

    private func setupBottomTabBar() {
        // 创建自定义底部TabBar
        customBottomTabBar = PromotionCenterTabBar()
        customBottomTabBar.delegate = self

        // 添加到主视图（不是contentView）
        view.addSubview(customBottomTabBar)
        customBottomTabBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(59 + view.safeAreaInsets.bottom)
        }

        // 设置默认选中项
        customBottomTabBar.setSelectedIndex(0)
    }
    
    // MARK: - Override BaseViewController Methods
    override func backButtonTapped() {
        // 所有子页面的返回按钮都返回到同一个上一页
        super.backButtonTapped()
    }
}

// MARK: - PromotionCenterTabBarDelegate
extension PromotionCenterMainViewController: PromotionCenterTabBarDelegate {
    func tabBar(_ tabBar: PromotionCenterTabBar, didSelectItemAt index: Int) {
        currentIndex = index

        // 切换到对应的页面
        listContainerView.didClickSelectedItem(at: index)

        // 根据选中的页面更新导航栏样式
        updateNavigationBarStyle(for: index)
    }
}
    
    private func updateNavigationBarStyle(for index: Int) {
        switch index {
        case 0:
            // 带货中心 - 橙色导航栏
            navBar.backgroundColor = UIColor(hex: "#FB6C04")
            titleLabel.textColor = .white
            // 使用白色返回按钮，如果没有则使用默认的
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "带货中心"

        case 1:
            // 销售中心 - 蓝色导航栏
            navBar.backgroundColor = UIColor(hex: "#007AFF")
            titleLabel.textColor = .white
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "销售中心"

        case 2:
            // 我的带货 - 紫色导航栏
            navBar.backgroundColor = UIColor(hex: "#8E44AD")
            titleLabel.textColor = .white
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "我的带货"

        default:
            // 默认样式
            navBar.backgroundColor = .white
            titleLabel.textColor = UIColor(hex: "#333333")
            backButton.setImage(UIImage(named: "nav_back"), for: .normal)
        }
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension PromotionCenterMainViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return subViewControllers.count
    }

    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        return subViewControllers[index] as! JXSegmentedListContainerViewListDelegate
    }
}

// MARK: - PromotionCenterTabBar
protocol PromotionCenterTabBarDelegate: AnyObject {
    func tabBar(_ tabBar: PromotionCenterTabBar, didSelectItemAt index: Int)
}

class PromotionCenterTabBar: UIView {

    weak var delegate: PromotionCenterTabBarDelegate?

    private var buttons: [PromotionCenterTabBarButton] = []
    private var selectedIndex: Int = 0

    // TabBar项配置
    private let tabItems = [
        PromotionCenterTabBarItem(
            title: "带货中心",
            normalIcon: "promotion_center_tab_normal",
            selectedIcon: "promotion_center_tab_selected"
        ),
        PromotionCenterTabBarItem(
            title: "销售中心",
            normalIcon: "sales_center_tab_normal",
            selectedIcon: "sales_center_tab_selected"
        ),
        PromotionCenterTabBarItem(
            title: "我的带货",
            normalIcon: "my_promotion_tab_normal",
            selectedIcon: "my_promotion_tab_selected"
        )
    ]

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .white

        // 添加顶部分隔线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
        addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }

        // 创建按钮
        setupButtons()
    }

    private func setupButtons() {
        // 清除现有按钮
        buttons.forEach { $0.removeFromSuperview() }
        buttons.removeAll()

        // 计算按钮宽度
        let buttonWidth = UIScreen.main.bounds.width / CGFloat(tabItems.count)

        for (index, item) in tabItems.enumerated() {
            let button = PromotionCenterTabBarButton(item: item)
            button.tag = index
            button.addTarget(self, action: #selector(buttonTapped(_:)), for: .touchUpInside)

            addSubview(button)
            buttons.append(button)

            button.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(8)
                make.bottom.equalTo(safeAreaLayoutGuide.snp.bottom).offset(-8)
                make.width.equalTo(buttonWidth)
                make.left.equalToSuperview().offset(CGFloat(index) * buttonWidth)
            }
        }

        // 设置默认选中状态
        updateSelectedState()
    }

    @objc private func buttonTapped(_ sender: PromotionCenterTabBarButton) {
        let index = sender.tag
        setSelectedIndex(index)
        delegate?.tabBar(self, didSelectItemAt: index)
    }

    func setSelectedIndex(_ index: Int) {
        guard index >= 0 && index < buttons.count else { return }
        selectedIndex = index
        updateSelectedState()
    }

    private func updateSelectedState() {
        for (index, button) in buttons.enumerated() {
            button.setSelected(index == selectedIndex)
        }
    }
}

// MARK: - PromotionCenterTabBarItem
struct PromotionCenterTabBarItem {
    let title: String
    let normalIcon: String
    let selectedIcon: String
}

// MARK: - PromotionCenterTabBarButton
class PromotionCenterTabBarButton: UIButton {

    private let item: PromotionCenterTabBarItem
    private let iconImageView: UIImageView
    private let titleLabel: UILabel

    init(item: PromotionCenterTabBarItem) {
        self.item = item
        self.iconImageView = UIImageView()
        self.titleLabel = UILabel()

        super.init(frame: .zero)

        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 配置图标
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.image = UIImage(named: item.normalIcon)
        addSubview(iconImageView)

        // 配置标题
        titleLabel.text = item.title
        titleLabel.font = UIFont.systemFont(ofSize: 10)
        titleLabel.textColor = UIColor(hex: "#999999")
        titleLabel.textAlignment = .center
        addSubview(titleLabel)

        // 设置约束
        iconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(4)
            make.width.height.equalTo(28)
        }

        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(iconImageView.snp.bottom).offset(2)
            make.left.right.equalToSuperview().inset(4)
        }
    }

    func setSelected(_ selected: Bool) {
        if selected {
            iconImageView.image = UIImage(named: item.selectedIcon)
            titleLabel.textColor = UIColor(hex: "#FB6C04")
        } else {
            iconImageView.image = UIImage(named: item.normalIcon)
            titleLabel.textColor = UIColor(hex: "#999999")
        }
    }
}
