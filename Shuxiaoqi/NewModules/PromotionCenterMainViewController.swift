//
//  PromotionCenterMainViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView

class PromotionCenterMainViewController: BaseViewController {
    
    // MARK: - Properties
    
    // 页面标题数组
    private let titles = ["带货中心", "销售中心", "我的带货"]
    
    // 子页面控制器数组
    private var childViewControllers: [UIViewController] = []
    
    // JX分段控制器
    private var segmentedView: JXSegmentedView!
    private var segmentedDataSource: JXSegmentedTitleDataSource!
    private var listContainerView: JXSegmentedListContainerView!
    
    // 当前选中的索引
    private var currentIndex: Int = 0
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置标题
        navTitle = "带货中心"

        // 设置视图背景色
        view.backgroundColor = .white
        contentView.backgroundColor = .white

        // 初始化子控制器
        setupChildViewControllers()

        // 设置UI
        setupUI()

        // 设置初始导航栏样式
        updateNavigationBarStyle(for: 0)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 确保导航栏在最上层
        bringNavBarToFront()
    }
    
    // MARK: - Setup
    private func setupChildViewControllers() {
        // 创建三个子页面控制器
        let promotionCenterVC = PromotionCenterViewController()
        let salesCenterVC = SalesCenterViewController()
        let myPromotionVC = MyPromotionViewController()
        
        childViewControllers = [promotionCenterVC, salesCenterVC, myPromotionVC]
    }
    
    private func setupUI() {
        // 设置JX分段控制器
        setupSegmentedView()
        
        // 设置列表容器
        setupListContainer()
    }
    
    private func setupSegmentedView() {
        // 创建数据源
        segmentedDataSource = JXSegmentedTitleDataSource()
        segmentedDataSource.titles = titles
        segmentedDataSource.titleNormalColor = UIColor(hex: "#666666")
        segmentedDataSource.titleSelectedColor = UIColor(hex: "#FB6C04")
        segmentedDataSource.titleNormalFont = UIFont.systemFont(ofSize: 16)
        segmentedDataSource.titleSelectedFont = UIFont.boldSystemFont(ofSize: 16)
        segmentedDataSource.itemSpacing = 40
        segmentedDataSource.isItemSpacingAverageEnabled = false
        
        // 创建分段控制器
        segmentedView = JXSegmentedView()
        segmentedView.backgroundColor = .white
        segmentedView.delegate = self
        segmentedView.dataSource = segmentedDataSource
        
        // 创建指示器
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor(hex: "#FB6C04")
        indicator.indicatorWidth = 20
        indicator.indicatorHeight = 3
        indicator.indicatorCornerRadius = 1.5
        segmentedView.indicators = [indicator]
        
        // 添加到视图
        contentView.addSubview(segmentedView)
        segmentedView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
    }
    
    private func setupListContainer() {
        // 创建列表容器
        listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.backgroundColor = .white
        
        // 关联分段控制器和列表容器
        segmentedView.listContainer = listContainerView
        
        // 添加到视图
        contentView.addSubview(listContainerView)
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Override BaseViewController Methods
    override func backButtonTapped() {
        // 所有子页面的返回按钮都返回到同一个上一页
        super.backButtonTapped()
    }
}

// MARK: - JXSegmentedViewDelegate
extension PromotionCenterMainViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        currentIndex = index
        
        // 根据选中的页面更新导航栏样式
        updateNavigationBarStyle(for: index)
    }
    
    private func updateNavigationBarStyle(for index: Int) {
        switch index {
        case 0:
            // 带货中心 - 橙色导航栏
            navBar.backgroundColor = UIColor(hex: "#FB6C04")
            titleLabel.textColor = .white
            // 使用白色返回按钮，如果没有则使用默认的
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "带货中心"

        case 1:
            // 销售中心 - 蓝色导航栏
            navBar.backgroundColor = UIColor(hex: "#007AFF")
            titleLabel.textColor = .white
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "销售中心"

        case 2:
            // 我的带货 - 紫色导航栏
            navBar.backgroundColor = UIColor(hex: "#8E44AD")
            titleLabel.textColor = .white
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "我的带货"

        default:
            // 默认样式
            navBar.backgroundColor = .white
            titleLabel.textColor = UIColor(hex: "#333333")
            backButton.setImage(UIImage(named: "nav_back"), for: .normal)
        }
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension PromotionCenterMainViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return childViewControllers.count
    }
    
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        return childViewControllers[index] as! JXSegmentedListContainerViewListDelegate
    }
}
